-- CC攻击防护系统 - Lua管理接口
-- 高性能的管理和监控接口

local redis = require "resty.redis"
local cjson = require "cjson"
local config = require "config"
local utils = require "utils"

local _M = {}

-- 获取Redis连接（复用access_control的连接逻辑）
local function get_redis_connection()
    local red = redis:new()
    red:set_timeout(config.redis.timeout or 1000)

    local ok, err = red:connect(config.redis.host or "127.0.0.1", config.redis.port or 6379)
    if not ok then
        return nil, err
    end

    if config.redis.password then
        local res, err = red:auth(config.redis.password)
        if not res then
            return nil, err
        end
    end

    if config.redis.database and config.redis.database > 0 then
        local res, err = red:select(config.redis.database)
        if not res then
            return nil, err
        end
    end

    return red
end

-- 关闭Redis连接
local function close_redis_connection(red)
    if red then
        red:set_keepalive(config.redis.keepalive or 60000, config.redis.pool_size or 100)
    end
end

-- 获取黑名单统计（高性能版本）
function _M.get_blacklist_stats()
    local red, err = get_redis_connection()
    if not red then
        return nil, err
    end

    -- 使用Lua脚本在Redis端执行，减少网络往返
    local lua_script = [[
        local keys = redis.call('keys', 'cc_protect:blacklist:*')
        local stats = {
            total_count = #keys,
            active_count = 0,
            expired_soon = 0,
            reasons = {}
        }

        for _, key in ipairs(keys) do
            local ttl = redis.call('ttl', key)
            if ttl > 0 then
                stats.active_count = stats.active_count + 1
                if ttl <= 3600 then
                    stats.expired_soon = stats.expired_soon + 1
                end
            end

            local data = redis.call('get', key)
            if data then
                local ok, info = pcall(cjson.decode, data)
                if ok and info.reason then
                    local reason = info.reason
                    stats.reasons[reason] = (stats.reasons[reason] or 0) + 1
                end
            end
        end

        return cjson.encode(stats)
    ]]

    local result, err = red:eval(lua_script, 0)
    close_redis_connection(red)

    if err then
        return nil, err
    end

    local ok, stats = pcall(cjson.decode, result)
    if not ok then
        return nil, "解析统计数据失败"
    end

    return stats
end

-- 获取实时监控数据（超高性能版本）
function _M.get_realtime_stats()
    local red, err = get_redis_connection()
    if not red then
        return nil, err
    end

    -- 单个Lua脚本获取所有统计数据
    local lua_script = [[
        local stats = {
            timestamp = redis.call('time')[1],
            blacklist_count = #redis.call('keys', 'cc_protect:blacklist:*'),
            ip_history_count = #redis.call('keys', 'cc_protect:ip_history:*'),
            whitelist_access_count = #redis.call('keys', 'cc_protect:ip_stats:*:whitelist_count'),
            active_blacklist = 0
        }

        -- 计算活跃黑名单数量
        local blacklist_keys = redis.call('keys', 'cc_protect:blacklist:*')
        for _, key in ipairs(blacklist_keys) do
            local ttl = redis.call('ttl', key)
            if ttl > 0 then
                stats.active_blacklist = stats.active_blacklist + 1
            end
        end

        return cjson.encode(stats)
    ]]

    local result, err = red:eval(lua_script, 0)
    close_redis_connection(red)

    if err then
        return nil, err
    end

    local ok, stats = pcall(cjson.decode, result)
    if not ok then
        return nil, "解析统计数据失败"
    end

    return stats
end

-- 批量操作黑名单（高性能版本）
function _M.batch_blacklist_operation(operation, ip_list, reason, ttl)
    local red, err = get_redis_connection()
    if not red then
        return nil, err
    end

    local lua_script
    if operation == "add" then
        lua_script = [[
            local ip_list = cjson.decode(ARGV[1])
            local reason = ARGV[2]
            local ttl = tonumber(ARGV[3])
            local timestamp = redis.call('time')[1]
            local success_count = 0

            for _, ip in ipairs(ip_list) do
                local key = 'cc_protect:blacklist:' .. ip
                local data = cjson.encode({
                    ip = ip,
                    reason = reason,
                    timestamp = tonumber(timestamp),
                    user_agent = "",
                    uri = "",
                    added_by = "Lua管理接口"
                })

                local result = redis.call('setex', key, ttl, data)
                if result then
                    success_count = success_count + 1
                end
            end

            return success_count
        ]]
    elseif operation == "remove" then
        lua_script = [[
            local ip_list = cjson.decode(ARGV[1])
            local success_count = 0

            for _, ip in ipairs(ip_list) do
                local key = 'cc_protect:blacklist:' .. ip
                local result = redis.call('del', key)
                success_count = success_count + result
            end

            return success_count
        ]]
    else
        close_redis_connection(red)
        return nil, "不支持的操作类型"
    end

    local success_count, err = red:eval(
        lua_script,
        0,
        cjson.encode(ip_list),
        reason or "批量操作",
        ttl or 3600
    )

    close_redis_connection(red)

    if err then
        return nil, err
    end

    return {
        operation = operation,
        total_count = #ip_list,
        success_count = success_count,
        success = success_count == #ip_list
    }
end

-- HTTP API处理器（高性能版本）
function _M.handle_api_request()
    local method = ngx.var.request_method
    local uri = ngx.var.uri
    local args = ngx.req.get_uri_args()

    -- 设置响应头
    ngx.header.content_type = "application/json; charset=utf-8"
    ngx.header["Access-Control-Allow-Origin"] = "*"

    local response = {
        success = false,
        message = "",
        data = nil,
        timestamp = ngx.time()
    }

    -- 路由处理
    if method == "GET" then
        if uri:match("/api/stats$") then
            local stats, err = _M.get_realtime_stats()
            if stats then
                response.success = true
                response.data = stats
            else
                response.message = err or "获取统计数据失败"
            end

        elseif uri:match("/api/blacklist/stats$") then
            local stats, err = _M.get_blacklist_stats()
            if stats then
                response.success = true
                response.data = stats
            else
                response.message = err or "获取黑名单统计失败"
            end

        elseif uri:match("/api/ip/(.+)$") then
            local ip = uri:match("/api/ip/(.+)$")
            if utils.is_valid_ip(ip) then
                local access_control = require "access_control"
                local info = access_control.get_ip_info(ip)
                if info then
                    response.success = true
                    response.data = info
                else
                    response.message = "获取IP信息失败"
                end
            else
                response.message = "无效的IP地址"
            end
        end

    elseif method == "POST" then
        ngx.req.read_body()
        local body_data = ngx.req.get_body_data()

        if uri:match("/api/blacklist/batch$") and body_data then
            local ok, data = pcall(cjson.decode, body_data)
            if ok and data.operation and data.ip_list then
                local result, err = _M.batch_blacklist_operation(
                    data.operation,
                    data.ip_list,
                    data.reason,
                    data.ttl
                )
                if result then
                    response.success = true
                    response.data = result
                else
                    response.message = err or "批量操作失败"
                end
            else
                response.message = "请求数据格式错误"
            end

        elseif uri:match("/api/blacklist/add$") and body_data then
            local ok, data = pcall(cjson.decode, body_data)
            if ok and data.ip then
                local access_control = require "access_control"
                local success = access_control.add_to_blacklist(data.ip, data.reason, data.ttl)
                if success then
                    response.success = true
                    response.message = "IP已添加到黑名单"
                else
                    response.message = "添加到黑名单失败"
                end
            else
                response.message = "请求数据格式错误"
            end
        end

    elseif method == "DELETE" then
        if uri:match("/api/blacklist/(.+)$") then
            local ip = uri:match("/api/blacklist/(.+)$")
            if utils.is_valid_ip(ip) then
                local access_control = require "access_control"
                local success = access_control.remove_from_blacklist(ip)
                if success then
                    response.success = true
                    response.message = "IP已从黑名单移除"
                else
                    response.message = "从黑名单移除失败"
                end
            else
                response.message = "无效的IP地址"
            end
        end
    end

    ngx.say(cjson.encode(response))
    ngx.exit(200)
end

-- 性能监控接口
function _M.get_performance_metrics()
    local red, err = get_redis_connection()
    if not red then
        return nil, err
    end

    local lua_script = [[
        local metrics = {
            redis_info = redis.call('info', 'memory'),
            key_counts = {
                blacklist = #redis.call('keys', 'cc_protect:blacklist:*'),
                ip_history = #redis.call('keys', 'cc_protect:ip_history:*'),
                ip_stats = #redis.call('keys', 'cc_protect:ip_stats:*')
            },
            memory_usage = redis.call('memory', 'usage', 'cc_protect:*') or 0
        }
        return cjson.encode(metrics)
    ]]

    local result, err = red:eval(lua_script, 0)
    close_redis_connection(red)

    if err then
        return nil, err
    end

    local ok, metrics = pcall(cjson.decode, result)
    if not ok then
        return nil, "解析性能指标失败"
    end

    return metrics
end

return _M