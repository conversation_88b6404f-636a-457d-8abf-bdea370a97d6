#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CC攻击防护系统 - 黑名单管理工具
提供黑名单的增删改查功能
"""

import redis
import json
import argparse
import sys
import time
from datetime import datetime
from typing import List, Dict, Optional

class BlacklistManager:
    def __init__(self, redis_host='127.0.0.1', redis_port=6379, redis_password=None, redis_db=0):
        """初始化黑名单管理器"""
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                db=redis_db,
                decode_responses=True
            )
            # 测试连接
            self.redis_client.ping()
            print(f"✓ 成功连接到Redis: {redis_host}:{redis_port}")
        except Exception as e:
            print(f"✗ 连接Redis失败: {e}")
            sys.exit(1)

    def add_ip(self, ip: str, reason: str = "手动添加", ttl: int = 3600) -> bool:
        """添加IP到黑名单"""
        try:
            key = f"cc_protect:blacklist:{ip}"
            data = {
                "ip": ip,
                "reason": reason,
                "timestamp": int(time.time()),
                "user_agent": "",
                "uri": "",
                "added_by": "管理工具"
            }

            result = self.redis_client.setex(key, ttl, json.dumps(data, ensure_ascii=False))
            if result:
                print(f"✓ 成功添加IP到黑名单: {ip}")
                print(f"  原因: {reason}")
                print(f"  过期时间: {ttl}秒")
                return True
            else:
                print(f"✗ 添加IP到黑名单失败: {ip}")
                return False
        except Exception as e:
            print(f"✗ 添加IP到黑名单时出错: {e}")
            return False

    def remove_ip(self, ip: str) -> bool:
        """从黑名单移除IP"""
        try:
            key = f"cc_protect:blacklist:{ip}"
            result = self.redis_client.delete(key)
            if result:
                print(f"✓ 成功从黑名单移除IP: {ip}")
                return True
            else:
                print(f"✗ IP不在黑名单中: {ip}")
                return False
        except Exception as e:
            print(f"✗ 移除IP时出错: {e}")
            return False

    def check_ip(self, ip: str) -> Optional[Dict]:
        """检查IP是否在黑名单中"""
        try:
            key = f"cc_protect:blacklist:{ip}"
            data = self.redis_client.get(key)
            if data:
                info = json.loads(data)
                ttl = self.redis_client.ttl(key)
                info['ttl'] = ttl
                return info
            return None
        except Exception as e:
            print(f"✗ 检查IP时出错: {e}")
            return None

    def list_all(self) -> List[Dict]:
        """列出所有黑名单IP"""
        try:
            pattern = "cc_protect:blacklist:*"
            keys = self.redis_client.keys(pattern)

            blacklist = []
            for key in keys:
                ip = key.split(':')[-1]
                data = self.redis_client.get(key)
                if data:
                    info = json.loads(data)
                    ttl = self.redis_client.ttl(key)
                    info['ttl'] = ttl
                    blacklist.append(info)

            return sorted(blacklist, key=lambda x: x.get('timestamp', 0), reverse=True)
        except Exception as e:
            print(f"✗ 获取黑名单时出错: {e}")
            return []

    def clear_all(self) -> bool:
        """清空所有黑名单"""
        try:
            pattern = "cc_protect:blacklist:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                result = self.redis_client.delete(*keys)
                print(f"✓ 成功清空黑名单，删除了 {result} 个IP")
                return True
            else:
                print("黑名单为空，无需清空")
                return True
        except Exception as e:
            print(f"✗ 清空黑名单时出错: {e}")
            return False

    def extend_ttl(self, ip: str, ttl: int) -> bool:
        """延长IP的黑名单时间"""
        try:
            key = f"cc_protect:blacklist:{ip}"
            if self.redis_client.exists(key):
                result = self.redis_client.expire(key, ttl)
                if result:
                    print(f"✓ 成功延长IP黑名单时间: {ip} -> {ttl}秒")
                    return True
            print(f"✗ IP不在黑名单中: {ip}")
            return False
        except Exception as e:
            print(f"✗ 延长TTL时出错: {e}")
            return False

def format_time(timestamp):
    """格式化时间戳"""
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def format_ttl(ttl):
    """格式化TTL"""
    if ttl < 0:
        return "永不过期"
    elif ttl < 60:
        return f"{ttl}秒"
    elif ttl < 3600:
        return f"{ttl//60}分{ttl%60}秒"
    else:
        hours = ttl // 3600
        minutes = (ttl % 3600) // 60
        return f"{hours}小时{minutes}分钟"

def print_blacklist_table(blacklist):
    """打印黑名单表格"""
    if not blacklist:
        print("黑名单为空")
        return

    print(f"\n{'IP地址':<15} {'添加时间':<19} {'剩余时间':<12} {'原因'}")
    print("-" * 70)

    for item in blacklist:
        ip = item.get('ip', 'N/A')
        timestamp = item.get('timestamp', 0)
        ttl = item.get('ttl', 0)
        reason = item.get('reason', 'N/A')

        add_time = format_time(timestamp)
        remaining = format_ttl(ttl)

        print(f"{ip:<15} {add_time:<19} {remaining:<12} {reason}")

def main():
    parser = argparse.ArgumentParser(description='CC攻击防护系统 - 黑名单管理工具')
    parser.add_argument('--host', default='127.0.0.1', help='Redis主机地址')
    parser.add_argument('--port', type=int, default=6379, help='Redis端口')
    parser.add_argument('--password', help='Redis密码')
    parser.add_argument('--db', type=int, default=0, help='Redis数据库')

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 添加IP命令
    add_parser = subparsers.add_parser('add', help='添加IP到黑名单')
    add_parser.add_argument('ip', help='要添加的IP地址')
    add_parser.add_argument('--reason', default='手动添加', help='添加原因')
    add_parser.add_argument('--ttl', type=int, default=3600, help='过期时间(秒)')

    # 移除IP命令
    remove_parser = subparsers.add_parser('remove', help='从黑名单移除IP')
    remove_parser.add_argument('ip', help='要移除的IP地址')

    # 检查IP命令
    check_parser = subparsers.add_parser('check', help='检查IP是否在黑名单中')
    check_parser.add_argument('ip', help='要检查的IP地址')

    # 列出所有IP命令
    list_parser = subparsers.add_parser('list', help='列出所有黑名单IP')

    # 清空黑名单命令
    clear_parser = subparsers.add_parser('clear', help='清空所有黑名单')
    clear_parser.add_argument('--confirm', action='store_true', help='确认清空操作')

    # 延长TTL命令
    extend_parser = subparsers.add_parser('extend', help='延长IP的黑名单时间')
    extend_parser.add_argument('ip', help='要延长的IP地址')
    extend_parser.add_argument('ttl', type=int, help='新的过期时间(秒)')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建黑名单管理器
    manager = BlacklistManager(
        redis_host=args.host,
        redis_port=args.port,
        redis_password=args.password,
        redis_db=args.db
    )

    # 执行命令
    if args.command == 'add':
        manager.add_ip(args.ip, args.reason, args.ttl)

    elif args.command == 'remove':
        manager.remove_ip(args.ip)

    elif args.command == 'check':
        info = manager.check_ip(args.ip)
        if info:
            print(f"✓ IP在黑名单中: {args.ip}")
            print(f"  添加时间: {format_time(info['timestamp'])}")
            print(f"  剩余时间: {format_ttl(info['ttl'])}")
            print(f"  原因: {info['reason']}")
            if info.get('uri'):
                print(f"  触发URL: {info['uri']}")
        else:
            print(f"✗ IP不在黑名单中: {args.ip}")

    elif args.command == 'list':
        blacklist = manager.list_all()
        print(f"黑名单总数: {len(blacklist)}")
        print_blacklist_table(blacklist)

    elif args.command == 'clear':
        if args.confirm:
            manager.clear_all()
        else:
            print("警告: 此操作将清空所有黑名单IP")
            print("请使用 --confirm 参数确认操作")

    elif args.command == 'extend':
        manager.extend_ttl(args.ip, args.ttl)

if __name__ == '__main__':
    main()