-- CC攻击防护模块
-- 基于IP访问模式分析的智能防护系统

local redis = require "resty.redis"
local cjson = require "cjson"
local config = require "config"
local utils = require "utils"

local _M = {}

-- Redis连接配置
local redis_config = {
    host = config.redis.host or "127.0.0.1",
    port = config.redis.port or 6379,
    password = config.redis.password,
    database = config.redis.database or 0,
    timeout = config.redis.timeout or 1000,
    pool_size = config.redis.pool_size or 100,
    keepalive = config.redis.keepalive or 60000
}

-- Redis键名前缀
local REDIS_PREFIX = {
    blacklist = "cc_protect:blacklist:",
    whitelist = "cc_protect:whitelist:",
    ip_history = "cc_protect:ip_history:",
    ip_stats = "cc_protect:ip_stats:",
    config = "cc_protect:config"
}

-- 获取Redis连接
local function get_redis_connection()
    local red = redis:new()
    red:set_timeout(redis_config.timeout)

    local ok, err = red:connect(redis_config.host, redis_config.port)
    if not ok then
        ngx.log(ngx.ERR, "Failed to connect to Redis: ", err)
        return nil, err
    end

    if redis_config.password then
        local res, err = red:auth(redis_config.password)
        if not res then
            ngx.log(ngx.ERR, "Failed to authenticate Redis: ", err)
            return nil, err
        end
    end

    if redis_config.database > 0 then
        local res, err = red:select(redis_config.database)
        if not res then
            ngx.log(ngx.ERR, "Failed to select Redis database: ", err)
            return nil, err
        end
    end

    return red
end

-- 关闭Redis连接
local function close_redis_connection(red)
    if red then
        local ok, err = red:set_keepalive(redis_config.keepalive, redis_config.pool_size)
        if not ok then
            ngx.log(ngx.ERR, "Failed to set Redis keepalive: ", err)
        end
    end
end

-- 检查IP是否在黑名单中
local function is_ip_blacklisted(red, ip)
    local key = REDIS_PREFIX.blacklist .. ip
    local result, err = red:get(key)

    if err then
        ngx.log(ngx.ERR, "Redis error checking blacklist: ", err)
        return false
    end

    return result ~= ngx.null and result
end

-- 将IP加入黑名单
local function add_ip_to_blacklist(red, ip, reason, ttl)
    local key = REDIS_PREFIX.blacklist .. ip
    local data = {
        ip = ip,
        reason = reason or "CC attack detected",
        timestamp = ngx.time(),
        user_agent = ngx.var.http_user_agent or "",
        uri = ngx.var.uri or ""
    }

    local ttl_seconds = ttl or config.blacklist_ttl or 3600  -- 默认1小时
    local result, err = red:setex(key, ttl_seconds, cjson.encode(data))

    if err then
        ngx.log(ngx.ERR, "Failed to add IP to blacklist: ", err)
        return false
    end

    -- 记录日志
    ngx.log(ngx.WARN, "IP blocked: ", ip, " Reason: ", reason)
    return true
end

-- 获取IP访问历史
local function get_ip_history(red, ip)
    local key = REDIS_PREFIX.ip_history .. ip
    local result, err = red:get(key)

    if err then
        ngx.log(ngx.ERR, "Redis error getting IP history: ", err)
        return {}
    end

    if result == ngx.null then
        return {}
    end

    local ok, history = pcall(cjson.decode, result)
    if not ok then
        ngx.log(ngx.ERR, "Failed to decode IP history: ", result)
        return {}
    end

    return history or {}
end

-- 更新IP访问历史
local function update_ip_history(red, ip, uri)
    local key = REDIS_PREFIX.ip_history .. ip
    local history = get_ip_history(red, ip)

    -- 添加当前访问记录
    local access_record = {
        uri = uri,
        timestamp = ngx.time(),
        user_agent = ngx.var.http_user_agent or ""
    }

    table.insert(history, access_record)

    -- 保持历史记录数量限制
    local max_history = config.max_history_records or 100
    if #history > max_history then
        table.remove(history, 1)  -- 移除最旧的记录
    end

    -- 保存到Redis，设置过期时间
    local ttl = config.history_ttl or 7200  -- 默认2小时
    local result, err = red:setex(key, ttl, cjson.encode(history))

    if err then
        ngx.log(ngx.ERR, "Failed to update IP history: ", err)
        return false
    end

    return true
end

-- 检查IP是否访问过必须访问的URL
local function has_visited_required_urls(red, ip)
    local history = get_ip_history(red, ip)
    local required_urls = config.required_urls or {}

    if #required_urls == 0 then
        return true  -- 如果没有配置必须访问的URL，则认为通过检查
    end

    -- 检查历史记录中是否包含必须访问的URL
    for _, record in ipairs(history) do
        for _, required_url in ipairs(required_urls) do
            if utils.match_url_pattern(record.uri, required_url) then
                return true
            end
        end
    end

    return false
end

-- 检查URL是否为敏感URL
local function is_sensitive_url(uri)
    local sensitive_urls = config.sensitive_urls or {}

    for _, pattern in ipairs(sensitive_urls) do
        if utils.match_url_pattern(uri, pattern) then
            return true
        end
    end

    return false
end

-- 检查URL是否为白名单URL
local function is_whitelist_url(uri)
    local whitelist_urls = config.whitelist_urls or {}

    for _, pattern in ipairs(whitelist_urls) do
        if utils.match_url_pattern(uri, pattern) then
            return true
        end
    end

    return false
end

-- 获取IP访问白名单URL的次数
local function get_whitelist_access_count(red, ip)
    local key = REDIS_PREFIX.ip_stats .. ip .. ":whitelist_count"
    local result, err = red:get(key)

    if err then
        ngx.log(ngx.ERR, "Redis error getting whitelist count: ", err)
        return 0
    end

    if result == ngx.null then
        return 0
    end

    return tonumber(result) or 0
end

-- 增加IP访问白名单URL的次数
local function increment_whitelist_access_count(red, ip)
    local key = REDIS_PREFIX.ip_stats .. ip .. ":whitelist_count"
    local ttl = config.stats_ttl or 7200  -- 默认2小时

    local result, err = red:incr(key)
    if err then
        ngx.log(ngx.ERR, "Failed to increment whitelist count: ", err)
        return false
    end

    -- 设置过期时间
    red:expire(key, ttl)

    return result
end

-- 主要的访问控制检查函数
function _M.check_access()
    local ip = utils.get_real_ip()
    local uri = ngx.var.uri

    if not ip or not uri then
        ngx.log(ngx.ERR, "Failed to get IP or URI")
        return
    end

    -- 获取Redis连接
    local red, err = get_redis_connection()
    if not red then
        ngx.log(ngx.ERR, "Failed to connect to Redis, allowing request")
        return  -- Redis连接失败时放行请求
    end

    -- 检查IP是否在黑名单中
    local blacklist_info = is_ip_blacklisted(red, ip)
    if blacklist_info then
        close_redis_connection(red)
        ngx.log(ngx.WARN, "Blocked request from blacklisted IP: ", ip)
        ngx.status = 403
        ngx.header["Content-Type"] = "text/html; charset=utf-8"
        ngx.say("访问被拒绝 - Access Denied")
        ngx.exit(403)
        return
    end

    -- 更新IP访问历史
    update_ip_history(red, ip, uri)

    local should_block = false
    local block_reason = ""

    -- 检查敏感URL访问
    if is_sensitive_url(uri) then
        if not has_visited_required_urls(red, ip) then
            should_block = true
            block_reason = "访问敏感URL但未访问必须URL"
        end
    end

    -- 检查白名单URL访问
    if is_whitelist_url(uri) then
        local access_count = get_whitelist_access_count(red, ip)
        increment_whitelist_access_count(red, ip)

        -- 第一次访问白名单URL直接放行，第二次开始检查
        if access_count >= 1 then  -- 第二次及以后的访问
            if not has_visited_required_urls(red, ip) then
                should_block = true
                block_reason = "多次访问白名单URL但未访问必须URL"
            end
        end
    end

    -- 如果需要拉黑IP
    if should_block then
        add_ip_to_blacklist(red, ip, block_reason)
        close_redis_connection(red)

        ngx.log(ngx.WARN, "Blocking IP: ", ip, " Reason: ", block_reason)
        ngx.status = 403
        ngx.header["Content-Type"] = "text/html; charset=utf-8"
        ngx.say("检测到可疑访问行为，访问被拒绝")
        ngx.exit(403)
        return
    end

    close_redis_connection(red)
end

-- 手动添加IP到黑名单的接口
function _M.add_to_blacklist(ip, reason, ttl)
    local red, err = get_redis_connection()
    if not red then
        return false, "Failed to connect to Redis"
    end

    local success = add_ip_to_blacklist(red, ip, reason, ttl)
    close_redis_connection(red)

    return success
end

-- 从黑名单移除IP的接口
function _M.remove_from_blacklist(ip)
    local red, err = get_redis_connection()
    if not red then
        return false, "Failed to connect to Redis"
    end

    local key = REDIS_PREFIX.blacklist .. ip
    local result, err = red:del(key)
    close_redis_connection(red)

    if err then
        return false, "Redis error: " .. err
    end

    return true
end

-- 获取IP的详细信息
function _M.get_ip_info(ip)
    local red, err = get_redis_connection()
    if not red then
        return nil, "Failed to connect to Redis"
    end

    local info = {
        ip = ip,
        is_blacklisted = false,
        blacklist_info = nil,
        access_history = {},
        whitelist_count = 0
    }

    -- 检查黑名单状态
    local blacklist_data = is_ip_blacklisted(red, ip)
    if blacklist_data then
        info.is_blacklisted = true
        local ok, data = pcall(cjson.decode, blacklist_data)
        if ok then
            info.blacklist_info = data
        end
    end

    -- 获取访问历史
    info.access_history = get_ip_history(red, ip)

    -- 获取白名单访问次数
    info.whitelist_count = get_whitelist_access_count(red, ip)

    close_redis_connection(red)
    return info
end

return _M