# Nginx配置示例 - CC攻击防护
# 请根据您的实际环境调整配置

# 在http块中添加Lua包路径
http {
    # 设置Lua包路径
    lua_package_path "/path/to/cc_protect/lua/?.lua;;";

    # 初始化配置
    init_by_lua_block {
        local config = require "config"
        local ok, err = config.init("/path/to/cc_protect/config/cc_protect.conf")
        if not ok then
            ngx.log(ngx.ERR, "Failed to initialize CC protect config: ", err)
        end
    }

    # 在server块中添加访问控制
    server {
        listen 80;
        server_name your-domain.com;

        # 设置真实IP
        set_real_ip_from 10.0.0.0/8;
        set_real_ip_from **********/12;
        set_real_ip_from ***********/16;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;

        # CC攻击防护 - 在access阶段执行
        access_by_lua_block {
            local access_control = require "access_control"
            access_control.check_access()
        }

        # 网站根目录
        location / {
            root /var/www/html;
            index index.html index.htm;
            try_files $uri $uri/ =404;
        }

        # 静态资源缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }

        # API接口
        location /api/ {
            # API接口会被标记为敏感URL，会进行严格检查
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 管理后台
        location /admin/ {
            # 管理后台也是敏感URL
            proxy_pass http://admin_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # CC防护管理接口
        location /cc_protect/ {
            access_by_lua_block {
                # 这里可以添加管理接口的认证逻辑
                local access_control = require "access_control"

                # 获取操作类型
                local action = ngx.var.arg_action
                local ip = ngx.var.arg_ip

                if action == "blacklist_add" and ip then
                    local reason = ngx.var.arg_reason or "Manual blacklist"
                    local ttl = tonumber(ngx.var.arg_ttl) or 3600
                    local success = access_control.add_to_blacklist(ip, reason, ttl)

                    ngx.header.content_type = "application/json"
                    if success then
                        ngx.say('{"status": "success", "message": "IP added to blacklist"}')
                    else
                        ngx.say('{"status": "error", "message": "Failed to add IP to blacklist"}')
                    end
                    ngx.exit(200)

                elseif action == "blacklist_remove" and ip then
                    local success = access_control.remove_from_blacklist(ip)

                    ngx.header.content_type = "application/json"
                    if success then
                        ngx.say('{"status": "success", "message": "IP removed from blacklist"}')
                    else
                        ngx.say('{"status": "error", "message": "Failed to remove IP from blacklist"}')
                    end
                    ngx.exit(200)

                elseif action == "ip_info" and ip then
                    local info = access_control.get_ip_info(ip)

                    ngx.header.content_type = "application/json"
                    if info then
                        ngx.say(require("cjson").encode(info))
                    else
                        ngx.say('{"status": "error", "message": "Failed to get IP info"}')
                    end
                    ngx.exit(200)
                end
            }

            return 404;
        }

        # 错误页面
        error_page 403 /403.html;
        location = /403.html {
            root /var/www/error;
            internal;
        }

        # 日志配置
        access_log /var/log/nginx/access.log combined;
        error_log /var/log/nginx/error.log warn;
    }

    # 后端服务器配置
    upstream backend {
        server 127.0.0.1:8080;
        # 可以添加更多后端服务器
    }

    upstream admin_backend {
        server 127.0.0.1:8081;
    }
}