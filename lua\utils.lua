-- 工具函数模块
-- 提供各种辅助功能

local _M = {}

-- 获取真实IP地址
function _M.get_real_ip()
    local ip = ngx.var.http_x_forwarded_for
    if ip then
        -- 处理多个IP的情况，取第一个
        ip = string.match(ip, "([^,]+)")
        if ip then
            ip = string.gsub(ip, "^%s*(.-)%s*$", "%1")  -- 去除空格
        end
    end

    if not ip or ip == "" then
        ip = ngx.var.http_x_real_ip
    end

    if not ip or ip == "" then
        ip = ngx.var.remote_addr
    end

    return ip
end

-- URL模式匹配
function _M.match_url_pattern(uri, pattern)
    if not uri or not pattern then
        return false
    end

    -- 简单的通配符匹配
    if pattern == "*" then
        return true
    end

    -- 精确匹配
    if uri == pattern then
        return true
    end

    -- 正则表达式匹配
    local ok, result = pcall(ngx.re.match, uri, pattern, "jo")
    if ok and result then
        return true
    end

    -- 简单的前缀匹配 (如果模式以 /* 结尾)
    if string.sub(pattern, -2) == "/*" then
        local prefix = string.sub(pattern, 1, -3)
        if string.sub(uri, 1, #prefix) == prefix then
            return true
        end
    end

    return false
end

-- 验证IP地址格式
function _M.is_valid_ip(ip)
    if not ip then
        return false
    end

    -- IPv4验证
    local parts = {}
    for part in string.gmatch(ip, "([^%.]+)") do
        table.insert(parts, part)
    end

    if #parts == 4 then
        for _, part in ipairs(parts) do
            local num = tonumber(part)
            if not num or num < 0 or num > 255 then
                return false
            end
        end
        return true
    end

    -- 简单的IPv6验证
    if string.find(ip, ":") then
        return string.match(ip, "^[0-9a-fA-F:]+$") ~= nil
    end

    return false
end

-- 获取当前时间戳
function _M.get_timestamp()
    return ngx.time()
end

-- 格式化时间
function _M.format_time(timestamp)
    if not timestamp then
        timestamp = ngx.time()
    end
    return os.date("%Y-%m-%d %H:%M:%S", timestamp)
end

-- 生成随机字符串
function _M.random_string(length)
    local chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local result = ""

    math.randomseed(ngx.time() + ngx.worker.pid())

    for i = 1, length or 8 do
        local rand = math.random(#chars)
        result = result .. string.sub(chars, rand, rand)
    end

    return result
end

-- 计算字符串MD5
function _M.md5(str)
    return ngx.md5(str or "")
end

-- URL编码
function _M.url_encode(str)
    if not str then
        return ""
    end

    str = string.gsub(str, "([^%w%-%.%_%~])", function(c)
        return string.format("%%%02X", string.byte(c))
    end)

    return str
end

-- URL解码
function _M.url_decode(str)
    if not str then
        return ""
    end

    str = string.gsub(str, "%%(%x%x)", function(h)
        return string.char(tonumber(h, 16))
    end)

    return str
end

-- 分割字符串
function _M.split(str, delimiter)
    if not str or not delimiter then
        return {}
    end

    local result = {}
    local pattern = "(.-)" .. delimiter
    local last_end = 1
    local s, e, cap = string.find(str, pattern, 1)

    while s do
        if s ~= 1 or cap ~= "" then
            table.insert(result, cap)
        end
        last_end = e + 1
        s, e, cap = string.find(str, pattern, last_end)
    end

    if last_end <= #str then
        cap = string.sub(str, last_end)
        table.insert(result, cap)
    end

    return result
end

-- 去除字符串首尾空格
function _M.trim(str)
    if not str then
        return ""
    end
    return string.gsub(str, "^%s*(.-)%s*$", "%1")
end

-- 检查字符串是否为空
function _M.is_empty(str)
    return not str or str == "" or string.match(str, "^%s*$")
end

-- 深度复制表
function _M.deep_copy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[_M.deep_copy(orig_key)] = _M.deep_copy(orig_value)
        end
        setmetatable(copy, _M.deep_copy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

return _M