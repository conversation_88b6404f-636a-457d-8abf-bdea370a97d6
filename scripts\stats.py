#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CC攻击防护系统 - 统计查看工具
提供系统运行状态和统计信息查看功能
"""

import redis
import json
import argparse
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from collections import defaultdict

class StatsViewer:
    def __init__(self, redis_host='127.0.0.1', redis_port=6379, redis_password=None, redis_db=0):
        """初始化统计查看器"""
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                db=redis_db,
                decode_responses=True
            )
            # 测试连接
            self.redis_client.ping()
            print(f"✓ 成功连接到Redis: {redis_host}:{redis_port}")
        except Exception as e:
            print(f"✗ 连接Redis失败: {e}")
            sys.exit(1)

    def get_blacklist_stats(self) -> Dict:
        """获取黑名单统计信息"""
        try:
            pattern = "cc_protect:blacklist:*"
            keys = self.redis_client.keys(pattern)

            total_count = len(keys)
            active_count = 0
            expired_soon = 0  # 1小时内过期

            reasons = defaultdict(int)

            for key in keys:
                ttl = self.redis_client.ttl(key)
                if ttl > 0:
                    active_count += 1
                    if ttl <= 3600:  # 1小时内过期
                        expired_soon += 1

                data = self.redis_client.get(key)
                if data:
                    try:
                        info = json.loads(data)
                        reason = info.get('reason', '未知')
                        reasons[reason] += 1
                    except:
                        pass

            return {
                'total_count': total_count,
                'active_count': active_count,
                'expired_soon': expired_soon,
                'reasons': dict(reasons)
            }
        except Exception as e:
            print(f"✗ 获取黑名单统计时出错: {e}")
            return {}

    def get_ip_history_stats(self) -> Dict:
        """获取IP访问历史统计"""
        try:
            pattern = "cc_protect:ip_history:*"
            keys = self.redis_client.keys(pattern)

            total_ips = len(keys)
            total_requests = 0
            url_stats = defaultdict(int)

            for key in keys:
                data = self.redis_client.get(key)
                if data:
                    try:
                        history = json.loads(data)
                        total_requests += len(history)

                        for record in history:
                            uri = record.get('uri', '')
                            url_stats[uri] += 1
                    except:
                        pass

            # 获取最热门的URL
            top_urls = sorted(url_stats.items(), key=lambda x: x[1], reverse=True)[:10]

            return {
                'total_ips': total_ips,
                'total_requests': total_requests,
                'top_urls': top_urls
            }
        except Exception as e:
            print(f"✗ 获取访问历史统计时出错: {e}")
            return {}

    def get_whitelist_stats(self) -> Dict:
        """获取白名单访问统计"""
        try:
            pattern = "cc_protect:ip_stats:*:whitelist_count"
            keys = self.redis_client.keys(pattern)

            total_ips = len(keys)
            total_whitelist_access = 0

            for key in keys:
                count = self.redis_client.get(key)
                if count:
                    try:
                        total_whitelist_access += int(count)
                    except:
                        pass

            return {
                'total_ips': total_ips,
                'total_whitelist_access': total_whitelist_access
            }
        except Exception as e:
            print(f"✗ 获取白名单统计时出错: {e}")
            return {}

    def get_redis_info(self) -> Dict:
        """获取Redis信息"""
        try:
            info = self.redis_client.info()
            return {
                'version': info.get('redis_version', 'N/A'),
                'uptime': info.get('uptime_in_seconds', 0),
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', 'N/A'),
                'total_commands_processed': info.get('total_commands_processed', 0)
            }
        except Exception as e:
            print(f"✗ 获取Redis信息时出错: {e}")
            return {}

    def get_ip_detail(self, ip: str) -> Dict:
        """获取特定IP的详细信息"""
        try:
            result = {
                'ip': ip,
                'blacklist_info': None,
                'access_history': [],
                'whitelist_count': 0
            }

            # 检查黑名单
            blacklist_key = f"cc_protect:blacklist:{ip}"
            blacklist_data = self.redis_client.get(blacklist_key)
            if blacklist_data:
                try:
                    info = json.loads(blacklist_data)
                    ttl = self.redis_client.ttl(blacklist_key)
                    info['ttl'] = ttl
                    result['blacklist_info'] = info
                except:
                    pass

            # 获取访问历史
            history_key = f"cc_protect:ip_history:{ip}"
            history_data = self.redis_client.get(history_key)
            if history_data:
                try:
                    result['access_history'] = json.loads(history_data)
                except:
                    pass

            # 获取白名单访问次数
            whitelist_key = f"cc_protect:ip_stats:{ip}:whitelist_count"
            whitelist_count = self.redis_client.get(whitelist_key)
            if whitelist_count:
                try:
                    result['whitelist_count'] = int(whitelist_count)
                except:
                    pass

            return result
        except Exception as e:
            print(f"✗ 获取IP详细信息时出错: {e}")
            return {}

def format_time(timestamp):
    """格式化时间戳"""
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def format_uptime(seconds):
    """格式化运行时间"""
    days = seconds // 86400
    hours = (seconds % 86400) // 3600
    minutes = (seconds % 3600) // 60
    return f"{days}天{hours}小时{minutes}分钟"

def print_overview(stats_viewer):
    """打印系统概览"""
    print("=" * 60)
    print("CC攻击防护系统 - 统计概览")
    print("=" * 60)

    # 黑名单统计
    blacklist_stats = stats_viewer.get_blacklist_stats()
    print(f"\n📋 黑名单统计:")
    print(f"  总数量: {blacklist_stats.get('total_count', 0)}")
    print(f"  活跃数量: {blacklist_stats.get('active_count', 0)}")
    print(f"  即将过期(1小时内): {blacklist_stats.get('expired_soon', 0)}")

    # 访问历史统计
    history_stats = stats_viewer.get_ip_history_stats()
    print(f"\n📊 访问历史统计:")
    print(f"  监控IP数量: {history_stats.get('total_ips', 0)}")
    print(f"  总请求数: {history_stats.get('total_requests', 0)}")

    # 白名单统计
    whitelist_stats = stats_viewer.get_whitelist_stats()
    print(f"\n🔍 白名单访问统计:")
    print(f"  访问白名单的IP数量: {whitelist_stats.get('total_ips', 0)}")
    print(f"  白名单总访问次数: {whitelist_stats.get('total_whitelist_access', 0)}")

    # Redis信息
    redis_info = stats_viewer.get_redis_info()
    print(f"\n🔧 Redis状态:")
    print(f"  版本: {redis_info.get('version', 'N/A')}")
    print(f"  运行时间: {format_uptime(redis_info.get('uptime', 0))}")
    print(f"  连接客户端: {redis_info.get('connected_clients', 0)}")
    print(f"  内存使用: {redis_info.get('used_memory', 'N/A')}")
    print(f"  处理命令总数: {redis_info.get('total_commands_processed', 0)}")

def print_blacklist_details(stats_viewer):
    """打印黑名单详细信息"""
    blacklist_stats = stats_viewer.get_blacklist_stats()

    print("\n📋 黑名单详细统计:")
    print(f"总数量: {blacklist_stats.get('total_count', 0)}")
    print(f"活跃数量: {blacklist_stats.get('active_count', 0)}")
    print(f"即将过期: {blacklist_stats.get('expired_soon', 0)}")

    reasons = blacklist_stats.get('reasons', {})
    if reasons:
        print("\n拉黑原因分布:")
        for reason, count in sorted(reasons.items(), key=lambda x: x[1], reverse=True):
            print(f"  {reason}: {count}")

def print_top_urls(stats_viewer):
    """打印热门URL"""
    history_stats = stats_viewer.get_ip_history_stats()
    top_urls = history_stats.get('top_urls', [])

    print("\n🔥 热门访问URL (Top 10):")
    if top_urls:
        for i, (url, count) in enumerate(top_urls, 1):
            print(f"  {i:2d}. {url:<40} ({count} 次)")
    else:
        print("  暂无数据")

def print_ip_details(stats_viewer, ip):
    """打印IP详细信息"""
    details = stats_viewer.get_ip_detail(ip)

    print(f"\n🔍 IP详细信息: {ip}")
    print("-" * 50)

    # 黑名单状态
    blacklist_info = details.get('blacklist_info')
    if blacklist_info:
        print("❌ 黑名单状态: 已拉黑")
        print(f"  拉黑时间: {format_time(blacklist_info.get('timestamp', 0))}")
        print(f"  拉黑原因: {blacklist_info.get('reason', 'N/A')}")
        ttl = blacklist_info.get('ttl', 0)
        if ttl > 0:
            print(f"  剩余时间: {ttl}秒")
        else:
            print("  剩余时间: 永不过期")
    else:
        print("✅ 黑名单状态: 未拉黑")

    # 白名单访问次数
    whitelist_count = details.get('whitelist_count', 0)
    print(f"📊 白名单访问次数: {whitelist_count}")

    # 访问历史
    access_history = details.get('access_history', [])
    print(f"📝 访问历史记录: {len(access_history)} 条")

    if access_history:
        print("\n最近访问记录:")
        # 显示最近10条记录
        recent_history = sorted(access_history, key=lambda x: x.get('timestamp', 0), reverse=True)[:10]
        for record in recent_history:
            timestamp = record.get('timestamp', 0)
            uri = record.get('uri', 'N/A')
            user_agent = record.get('user_agent', 'N/A')[:50]
            print(f"  {format_time(timestamp)} - {uri}")
            if user_agent != 'N/A':
                print(f"    UA: {user_agent}...")

def main():
    parser = argparse.ArgumentParser(description='CC攻击防护系统 - 统计查看工具')
    parser.add_argument('--host', default='127.0.0.1', help='Redis主机地址')
    parser.add_argument('--port', type=int, default=6379, help='Redis端口')
    parser.add_argument('--password', help='Redis密码')
    parser.add_argument('--db', type=int, default=0, help='Redis数据库')

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 概览命令
    overview_parser = subparsers.add_parser('overview', help='显示系统概览')

    # 黑名单统计命令
    blacklist_parser = subparsers.add_parser('blacklist', help='显示黑名单详细统计')

    # 热门URL命令
    urls_parser = subparsers.add_parser('urls', help='显示热门访问URL')

    # IP详情命令
    ip_parser = subparsers.add_parser('ip', help='显示特定IP的详细信息')
    ip_parser.add_argument('ip', help='要查看的IP地址')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建统计查看器
    stats_viewer = StatsViewer(
        redis_host=args.host,
        redis_port=args.port,
        redis_password=args.password,
        redis_db=args.db
    )

    # 执行命令
    if args.command == 'overview':
        print_overview(stats_viewer)

    elif args.command == 'blacklist':
        print_blacklist_details(stats_viewer)

    elif args.command == 'urls':
        print_top_urls(stats_viewer)

    elif args.command == 'ip':
        print_ip_details(stats_viewer, args.ip)

if __name__ == '__main__':
    main()