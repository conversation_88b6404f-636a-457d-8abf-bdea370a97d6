# CC攻击防护系统

基于Nginx + Lua + Redis的智能CC攻击防护系统，通过分析IP访问模式来识别和阻止CC攻击。

## 系统特性

- **智能检测**: 基于IP访问模式分析，识别异常访问行为
- **实时防护**: 在Nginx层实时检测和阻止恶意请求
- **灵活配置**: 支持动态配置敏感URL、白名单URL和必须访问URL
- **高性能**: 使用Redis缓存，毫秒级响应
- **易于管理**: 提供完整的管理工具和监控界面

## 防护原理

1. **敏感URL检测**: 当IP访问敏感URL时，检查是否访问过必须访问的URL列表
2. **白名单URL检测**: 白名单URL第一次访问直接放行，第二次开始检测访问历史
3. **访问模式分析**: 识别只访问单一URL的可疑行为模式
4. **自动拉黑**: 检测到CC攻击模式时自动将IP加入黑名单

## 目录结构

```
cc_protect/
├── lua/                    # Lua脚本目录
│   ├── access_control.lua  # 主要访问控制逻辑
│   ├── config.lua         # 配置加载模块
│   └── utils.lua          # 工具函数
├── config/                # 配置文件目录
│   ├── cc_protect.conf    # 主配置文件
│   └── nginx.conf         # Nginx配置示例
├── scripts/               # 管理脚本目录
│   ├── manage_blacklist.py # 黑名单管理
│   ├── stats.py           # 统计查看
│   └── monitor.py         # 监控脚本
├── docs/                  # 文档目录
└── README.md              # 说明文档
```

## 快速开始

1. 安装依赖
2. 配置Redis
3. 配置Nginx
4. 启动服务

详细部署说明请参考 `docs/deployment.md`