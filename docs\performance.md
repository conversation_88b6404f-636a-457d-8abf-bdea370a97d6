# CC攻击防护系统 - 性能优化指南

## 架构设计理念

### 分层性能优化策略

```
┌─────────────────────────────────────────────────────────────┐
│                    性能优化分层架构                          │
├─────────────────────────────────────────────────────────────┤
│ 🚀 实时处理层 (Lua)     │ 毫秒级响应，每秒处理万级请求      │
│ ├─ access_control.lua   │ 核心访问控制逻辑                  │
│ ├─ admin.lua           │ 高性能管理接口                    │
│ └─ utils.lua           │ 工具函数库                        │
├─────────────────────────────────────────────────────────────┤
│ 📊 管理工具层 (Python)  │ 秒级响应，功能丰富易用            │
│ ├─ manage_blacklist.py │ 黑名单管理工具                    │
│ ├─ stats.py           │ 统计分析工具                      │
│ └─ monitor.py          │ 监控告警工具                      │
├─────────────────────────────────────────────────────────────┤
│ 💾 数据存储层 (Redis)   │ 内存级访问，支持持久化            │
│ ├─ 黑名单数据          │ 带TTL的键值存储                   │
│ ├─ 访问历史            │ JSON格式历史记录                  │
│ └─ 统计数据            │ 计数器和聚合数据                  │
└─────────────────────────────────────────────────────────────┘
```

## 性能对比分析

### Lua vs Python 性能对比

| 指标 | Lua (OpenResty) | Python | 性能提升 |
|------|-----------------|--------|----------|
| **响应时间** | 0.1-1ms | 10-50ms | **10-50倍** |
| **内存占用** | 2-5MB | 20-100MB | **4-20倍** |
| **并发处理** | 10,000+ QPS | 500-2,000 QPS | **5-20倍** |
| **启动时间** | 即时 | 1-3秒 | **即时启动** |
| **CPU使用** | 极低 | 中等 | **显著降低** |

### 实际场景性能测试

#### 1. 黑名单检查性能
```lua
-- Lua版本 (在Nginx worker中执行)
local start_time = ngx.now()
local is_blacklisted = check_ip_blacklisted(ip)
local end_time = ngx.now()
-- 平均耗时: 0.1-0.5ms
```

```python
# Python版本 (独立进程)
import time
start_time = time.time()
is_blacklisted = check_ip_blacklisted(ip)
end_time = time.time()
# 平均耗时: 5-20ms (包含进程间通信)
```

#### 2. 批量操作性能
```lua
-- Lua版本: 使用Redis Lua脚本
-- 1000个IP批量操作: 10-50ms
local result = batch_blacklist_operation("add", ip_list, reason, ttl)
```

```python
# Python版本: 循环操作
# 1000个IP批量操作: 200-1000ms
for ip in ip_list:
    add_to_blacklist(ip, reason, ttl)
```

## 性能优化技术

### 1. Lua层优化

#### Redis连接池复用
```lua
-- 使用OpenResty的连接池
local red = redis:new()
red:set_timeout(1000)
-- 连接复用，避免频繁建立连接
red:set_keepalive(60000, 100)
```

#### Lua脚本服务端执行
```lua
-- 在Redis服务端执行复杂逻辑，减少网络往返
local lua_script = [[
    local keys = redis.call('keys', 'cc_protect:blacklist:*')
    local stats = { total_count = #keys, active_count = 0 }
    for _, key in ipairs(keys) do
        if redis.call('ttl', key) > 0 then
            stats.active_count = stats.active_count + 1
        end
    end
    return cjson.encode(stats)
]]
local result = red:eval(lua_script, 0)
```

#### 批量操作优化
```lua
-- 使用pipeline减少网络往返
red:init_pipeline()
for _, ip in ipairs(ip_list) do
    red:get("cc_protect:blacklist:" .. ip)
end
local results = red:commit_pipeline()
```

### 2. Redis层优化

#### 数据结构优化
```redis
# 使用合适的数据类型和过期策略
SETEX cc_protect:blacklist:*********** 3600 '{"ip":"***********","reason":"CC attack"}'

# 使用Hash结构存储复杂数据
HSET cc_protect:ip_stats:*********** whitelist_count 5
EXPIRE cc_protect:ip_stats:*********** 7200
```

#### 内存优化配置
```redis
# redis.conf 优化配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Nginx层优化

#### 工作进程配置
```nginx
# nginx.conf 性能优化
worker_processes auto;
worker_connections 1024;
worker_rlimit_nofile 2048;

# Lua相关优化
lua_code_cache on;
lua_shared_dict cc_protect_cache 10m;
lua_shared_dict cc_protect_locks 1m;
```

#### 缓存策略
```lua
-- 使用共享内存缓存热点数据
local cache = ngx.shared.cc_protect_cache
local cached_result = cache:get("blacklist:" .. ip)
if cached_result then
    return cached_result == "1"
end

-- 缓存结果，减少Redis访问
cache:set("blacklist:" .. ip, is_blacklisted and "1" or "0", 60)
```

## 使用建议

### 何时使用Lua接口
- ✅ 实时访问控制检查
- ✅ 高频率的状态查询
- ✅ 需要毫秒级响应的操作
- ✅ 与Nginx紧密集成的功能

### 何时使用Python工具
- ✅ 复杂的数据分析和报表
- ✅ 批量数据处理和导入导出
- ✅ 定时任务和后台处理
- ✅ 与其他系统的集成

### 混合使用策略
```bash
# 实时监控使用Lua接口
curl http://your-domain.com/api/stats

# 详细分析使用Python工具
python3 scripts/stats.py overview

# 批量管理使用Python工具
python3 scripts/manage_blacklist.py add ***********00 --reason "手动添加"

# 紧急操作使用Lua接口
curl -X POST http://your-domain.com/api/blacklist/batch \
  -d '{"operation":"add","ip_list":["***********","***********"]}'
```

## 性能监控

### 关键指标监控
```lua
-- 在Lua中监控性能指标
local start_time = ngx.now()
-- 执行操作
local end_time = ngx.now()
local duration = (end_time - start_time) * 1000  -- 转换为毫秒

-- 记录到日志
ngx.log(ngx.INFO, "Operation took: ", duration, "ms")
```

### 性能基准测试
```bash
# 使用ab测试Lua接口性能
ab -n 10000 -c 100 http://your-domain.com/api/stats

# 预期结果:
# Requests per second: 8000-15000 [#/sec]
# Time per request: 6-12 [ms] (mean)
```

通过这种分层架构，我们实现了：
- **实时处理**：Lua确保毫秒级响应
- **功能丰富**：Python提供强大的管理工具
- **资源高效**：合理分配计算资源
- **易于维护**：清晰的职责分离