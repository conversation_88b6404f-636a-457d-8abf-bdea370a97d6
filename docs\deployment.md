# CC攻击防护系统 - 部署指南

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Nginx**: 1.18+ 带OpenResty或lua-resty模块
- **Redis**: 5.0+
- **Python**: 3.7+ (可选，用于管理工具)

## 安装步骤

### 1. 安装OpenResty

#### Ubuntu/Debian
```bash
# 添加OpenResty仓库
wget -qO - https://openresty.org/package/pubkey.gpg | sudo apt-key add -
echo "deb http://openresty.org/package/ubuntu $(lsb_release -sc) main" | sudo tee /etc/apt/sources.list.d/openresty.list

# 安装OpenResty
sudo apt-get update
sudo apt-get install openresty
```

#### CentOS/RHEL
```bash
# 添加OpenResty仓库
sudo yum install yum-utils
sudo yum-config-manager --add-repo https://openresty.org/package/centos/openresty.repo

# 安装OpenResty
sudo yum install openresty
```

### 2. 安装Redis

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install redis-server
```

#### CentOS/RHEL
```bash
sudo yum install epel-release
sudo yum install redis
```

#### 启动Redis服务
```bash
sudo systemctl start redis
sudo systemctl enable redis
```

### 3. 安装Python依赖 (可选)

```bash
# 安装Python包管理器
sudo apt-get install python3-pip  # Ubuntu/Debian
sudo yum install python3-pip      # CentOS/RHEL

# 安装Redis Python客户端
pip3 install redis
```

### 4. 部署CC防护系统

#### 下载并部署代码
```bash
# 创建部署目录
sudo mkdir -p /opt/cc_protect
cd /opt/cc_protect

# 复制项目文件
sudo cp -r /path/to/cc_protect/* /opt/cc_protect/

# 设置权限
sudo chown -R www-data:www-data /opt/cc_protect  # Ubuntu
sudo chown -R nginx:nginx /opt/cc_protect        # CentOS
```

#### 配置文件设置
```bash
# 编辑配置文件
sudo vim /opt/cc_protect/config/cc_protect.conf

# 根据实际环境修改Redis连接信息
{
    "redis": {
        "host": "127.0.0.1",
        "port": 6379,
        "password": null,
        "database": 0
    },
    "sensitive_urls": [
        "/api/.*",
        "/admin/.*",
        "/login"
    ],
    "whitelist_urls": [
        "/",
        "/index.html",
        "/static/.*"
    ],
    "required_urls": [
        "/",
        "/index.html"
    ]
}
```

### 5. 配置Nginx

#### 备份原配置
```bash
sudo cp /usr/local/openresty/nginx/conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf.backup
```

#### 修改Nginx配置
```bash
sudo vim /usr/local/openresty/nginx/conf/nginx.conf
```

添加以下配置：
```nginx
http {
    # 设置Lua包路径
    lua_package_path "/opt/cc_protect/lua/?.lua;;";

    # 初始化配置
    init_by_lua_block {
        local config = require "config"
        local ok, err = config.init("/opt/cc_protect/config/cc_protect.conf")
        if not ok then
            ngx.log(ngx.ERR, "Failed to initialize CC protect config: ", err)
        end
    }

    # 共享内存配置
    lua_shared_dict cc_protect_cache 10m;
    lua_shared_dict cc_protect_locks 1m;

    server {
        listen 80;
        server_name your-domain.com;

        # 设置真实IP
        set_real_ip_from 10.0.0.0/8;
        set_real_ip_from **********/12;
        set_real_ip_from ***********/16;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;

        # CC攻击防护
        access_by_lua_block {
            local access_control = require "access_control"
            access_control.check_access()
        }

        # 管理API接口
        location /api/ {
            access_by_lua_block {
                local admin = require "admin"
                admin.handle_api_request()
            }
        }

        # 网站内容
        location / {
            root /var/www/html;
            index index.html;
        }
    }
}
```

### 6. 启动服务

#### 测试Nginx配置
```bash
sudo /usr/local/openresty/nginx/sbin/nginx -t
```

#### 启动Nginx
```bash
sudo /usr/local/openresty/nginx/sbin/nginx
```

#### 设置开机自启
```bash
# 创建systemd服务文件
sudo vim /etc/systemd/system/openresty.service
```

添加以下内容：
```ini
[Unit]
Description=OpenResty
After=network.target

[Service]
Type=forking
PIDFile=/usr/local/openresty/nginx/logs/nginx.pid
ExecStartPre=/usr/local/openresty/nginx/sbin/nginx -t
ExecStart=/usr/local/openresty/nginx/sbin/nginx
ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/bin/kill -s QUIT $MAINPID
PrivateTmp=true

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable openresty
sudo systemctl start openresty
```