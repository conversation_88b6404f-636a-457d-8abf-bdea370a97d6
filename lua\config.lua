-- 配置管理模块
-- 负责加载和管理CC防护系统的配置

local cjson = require "cjson"
local _M = {}

-- 默认配置
local default_config = {
    -- Redis配置
    redis = {
        host = "127.0.0.1",
        port = 6379,
        password = nil,
        database = 0,
        timeout = 1000,
        pool_size = 100,
        keepalive = 60000
    },

    -- 敏感URL列表 (支持正则表达式)
    sensitive_urls = {
        "/api/.*",
        "/admin/.*",
        "/login",
        "/register",
        "/upload",
        "/download/.*"
    },

    -- 白名单URL列表 (支持正则表达式)
    whitelist_urls = {
        "/",
        "/index.html",
        "/home",
        "/about",
        "/contact",
        "/static/.*",
        "/css/.*",
        "/js/.*",
        "/images/.*"
    },

    -- 必须访问的URL列表 (支持正则表达式)
    required_urls = {
        "/",
        "/index.html",
        "/home"
    },

    -- 黑名单TTL (秒)
    blacklist_ttl = 3600,  -- 1小时

    -- 访问历史TTL (秒)
    history_ttl = 7200,    -- 2小时

    -- 统计数据TTL (秒)
    stats_ttl = 7200,      -- 2小时

    -- 最大历史记录数量
    max_history_records = 100,

    -- 是否启用调试模式
    debug_mode = false,

    -- 日志级别
    log_level = "warn"
}

-- 当前配置
local current_config = {}

-- 从文件加载配置
local function load_config_from_file(file_path)
    local file = io.open(file_path, "r")
    if not file then
        return nil, "Cannot open config file: " .. file_path
    end

    local content = file:read("*all")
    file:close()

    if not content or content == "" then
        return nil, "Config file is empty"
    end

    local ok, config = pcall(cjson.decode, content)
    if not ok then
        return nil, "Invalid JSON in config file: " .. config
    end

    return config
end

-- 合并配置
local function merge_config(base, override)
    local result = {}

    -- 复制基础配置
    for k, v in pairs(base) do
        if type(v) == "table" then
            result[k] = merge_config(v, {})
        else
            result[k] = v
        end
    end

    -- 覆盖配置
    for k, v in pairs(override) do
        if type(v) == "table" and type(result[k]) == "table" then
            result[k] = merge_config(result[k], v)
        else
            result[k] = v
        end
    end

    return result
end

-- 验证配置
local function validate_config(config)
    local errors = {}

    -- 验证Redis配置
    if not config.redis then
        table.insert(errors, "Missing redis configuration")
    else
        if not config.redis.host then
            table.insert(errors, "Missing redis.host")
        end
        if not config.redis.port or type(config.redis.port) ~= "number" then
            table.insert(errors, "Invalid redis.port")
        end
    end

    -- 验证URL列表
    if config.sensitive_urls and type(config.sensitive_urls) ~= "table" then
        table.insert(errors, "sensitive_urls must be an array")
    end

    if config.whitelist_urls and type(config.whitelist_urls) ~= "table" then
        table.insert(errors, "whitelist_urls must be an array")
    end

    if config.required_urls and type(config.required_urls) ~= "table" then
        table.insert(errors, "required_urls must be an array")
    end

    -- 验证TTL配置
    if config.blacklist_ttl and (type(config.blacklist_ttl) ~= "number" or config.blacklist_ttl <= 0) then
        table.insert(errors, "blacklist_ttl must be a positive number")
    end

    if #errors > 0 then
        return false, table.concat(errors, "; ")
    end

    return true
end

-- 初始化配置
function _M.init(config_file)
    -- 从默认配置开始
    current_config = merge_config(default_config, {})

    -- 如果指定了配置文件，则加载并合并
    if config_file then
        local file_config, err = load_config_from_file(config_file)
        if file_config then
            current_config = merge_config(current_config, file_config)
        else
            ngx.log(ngx.WARN, "Failed to load config file: ", err, ", using default config")
        end
    end

    -- 验证配置
    local valid, err = validate_config(current_config)
    if not valid then
        ngx.log(ngx.ERR, "Invalid configuration: ", err)
        return false, err
    end

    -- 设置全局配置
    for k, v in pairs(current_config) do
        _M[k] = v
    end

    if current_config.debug_mode then
        ngx.log(ngx.INFO, "CC Protect configuration loaded successfully")
    end

    return true
end

-- 获取当前配置
function _M.get_config()
    return current_config
end

-- 重新加载配置
function _M.reload(config_file)
    return _M.init(config_file)
end

-- 获取特定配置项
function _M.get(key)
    return current_config[key]
end

-- 设置特定配置项 (运行时修改)
function _M.set(key, value)
    current_config[key] = value
    _M[key] = value
end

return _M